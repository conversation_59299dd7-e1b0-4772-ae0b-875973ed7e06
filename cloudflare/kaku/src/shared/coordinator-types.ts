/**
 * Shared types for CoordinatorDO functionality
 *
 * This file contains TypeScript interfaces and types used by the CoordinatorDO
 * for managing links and Agent DOs across platform connections.
 */

import { PlatformTypes } from '../ui/constants';

/**
 * CoordinatorDO State Interface
 * Manages platform metadata with links and session data
 */
export interface CoordinatorState {
  /** List of platform metadata */
  platforms: PlatformMetadata[];
}

/**
 * Platform metadata containing links and session data
 */
export interface PlatformMetadata {
  /** Platform identifier */
  id: PlatformTypes;
  /** List of links for this platform */
  links: LinkInfo[];
  /** Session data for this platform */
  sessionData?: any;
  /** Whether this platform is connected */
  connected: boolean;
  /** Current retry count for this platform */
  retryCount: number;
}

/**
 * Link information within platform metadata
 */
export interface LinkInfo {
  /** Current status of the link */
  status: 'active' | 'expired' | 'connected' | 'not_available';
  /** URL/identifier for the link */
  url: string;
  /** Timestamp when link was created */
  createdAt: number;
  /** Timestamp when link expires (24 hours from creation) */
  expiresAt: number;
  /** Timestamp when link was connected (if applicable) */
  connectedAt?: number;
  /** Current AgentDO identifier for this link */
  agentId?: string;
}

/**
 * Platform-specific retry configurations
 */
export const PLATFORM_RETRY_LIMIT = 3;

/**
 * Response types for CoordinatorDO RPC methods
 */
export interface CreateLinkResponse {
  linkId: string;
  url: string;
  expiresAt: number;
}

export interface ResetLinkResponse {
  newAgentId: string;
}

/**
 * Link status types
 */
export type LinkStatus = 'active' | 'expired' | 'connected' | 'not_available';

/**
 * Coordinator status types
 */
export type CoordinatorStatus = 'initializing' | 'active' | 'error';

/**
 * Utility functions for coordinator operations
 */
export class CoordinatorUtils {
  /**
   * Generate a unique link ID
   */
  static generateLinkId(): string {
    return `l_${crypto.randomUUID()}`;
  }

  /**
   * Check if a link has expired
   */
  static isLinkExpired(link: LinkInfo): boolean {
    return Date.now() > link.expiresAt;
  }

  /**
   * Check if retries are available for a platform
   */
  static canRetry(retryCount: number): boolean {
    return retryCount < PLATFORM_RETRY_LIMIT;
  }

  /**
   * Calculate expiration time (24 hours from now)
   */
  static calculateExpirationTime(): number {
    return Date.now() + 24 * 60 * 60 * 1000;
  }

  /**
   * Calculate deletion time (7 days from expiration)
   */
  static calculateDeletionTime(expirationTime: number): number {
    return expirationTime + 7 * 24 * 60 * 60 * 1000;
  }

  /**
   * Check if a link should be deleted (expired more than 7 days ago)
   */
  static shouldDelete(link: LinkInfo): boolean {
    if (link.status !== 'expired') return false;
    const sevenDaysAfterExpiration = link.expiresAt + 7 * 24 * 60 * 60 * 1000;
    return Date.now() > sevenDaysAfterExpiration;
  }
}

/**
 * Constants for coordinator operations
 */
export const COORDINATOR_CONSTANTS = {
  /** Link expiration time in milliseconds (24 hours) */
  LINK_EXPIRATION_TIME: 24 * 60 * 60 * 1000,

  /** Agent deletion delay in milliseconds (7 days) */
  AGENT_DELETION_DELAY: 7 * 24 * 60 * 60 * 1000,

  /** Default cleanup interval in milliseconds (1 hour) */
  CLEANUP_INTERVAL: 60 * 60 * 1000,
} as const;
